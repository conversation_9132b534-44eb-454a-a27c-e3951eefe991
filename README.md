<p align="center"><a href="https://laravel.com" target="_blank"><img src="https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg" width="400" alt="Laravel Logo"></a></p>

<p align="center">
<a href="https://github.com/laravel/framework/actions"><img src="https://github.com/laravel/framework/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/dt/laravel/framework" alt="Total Downloads"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/v/laravel/framework" alt="Latest Stable Version"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/l/laravel/framework" alt="License"></a>
</p>

## About Laravel

Laravel is a web application framework with expressive, elegant syntax. We believe development must be an enjoyable and creative experience to be truly fulfilling. Laravel takes the pain out of development by easing common tasks used in many web projects, such as:

- [Simple, fast routing engine](https://laravel.com/docs/routing).
- [Powerful dependency injection container](https://laravel.com/docs/container).
- Multiple back-ends for [session](https://laravel.com/docs/session) and [cache](https://laravel.com/docs/cache) storage.
- Expressive, intuitive [database ORM](https://laravel.com/docs/eloquent).
- Database agnostic [schema migrations](https://laravel.com/docs/migrations).
- [Robust background job processing](https://laravel.com/docs/queues).
- [Real-time event broadcasting](https://laravel.com/docs/broadcasting).

Laravel is accessible, powerful, and provides tools required for large, robust applications.

## Learning Laravel

Laravel has the most extensive and thorough [documentation](https://laravel.com/docs) and video tutorial library of all modern web application frameworks, making it a breeze to get started with the framework.

You may also try the [Laravel Bootcamp](https://bootcamp.laravel.com), where you will be guided through building a modern Laravel application from scratch.

If you don't feel like reading, [Laracasts](https://laracasts.com) can help. Laracasts contains thousands of video tutorials on a range of topics including Laravel, modern PHP, unit testing, and JavaScript. Boost your skills by digging into our comprehensive video library.

## نظام ERP لشركات الأمن والحراسة

## 🛡️ نظرة عامة

نظام ERP متكامل وحديث مصمم خصيصاً لإدارة شركات الأمن والحراسة بكفاءة عالية. يوفر حلول تقنية متقدمة لتحسين العمليات التشغيلية وزيادة الربحية.

## ✨ الميزات الرئيسية

### 🔧 الموديولات الأساسية
- **👥 إدارة الموظفين (MOD-EMP)**: بيانات شاملة وإدارة ملفات رقمية
- **⏰ الحضور والانصراف (MOD-ATT)**: تسجيل يدوي أو بالكاميرا مع QR Code
- **💰 المرتبات (MOD-PAY)**: حساب تلقائي من بيانات الحضور
- **📊 الحسابات (MOD-ACC)**: إدارة المصروفات والإيرادات والفواتير
- **📦 إدارة المخزن (MOD-STK)**: إضافة وصرف وتسوية وجرد
- **🏢 الموردين (MOD-VEN)**: بيانات الموردين وفواتير الشراء
- **👥 العملاء (MOD-CLI)**: إدارة العملاء والعقود والخدمات
- **📋 التقارير (MOD-RPT)**: تقارير PDF وExcel شاملة

### 🚀 الموديولات المتقدمة (مستقبلية)
- **📍 إدارة المواقع (MOD-SITE)**: خرائط تفاعلية وتوزيع الحراس
- **🔄 إدارة الورديات (MOD-SHIFT)**: جدولة وتبديل الورديات
- **🚨 إدارة الحوادث (MOD-INCIDENT)**: تسجيل ومتابعة الحوادث
- **📚 إدارة التدريب (MOD-TRAINING)**: برامج تدريبية وشهادات
- **🚗 إدارة المركبات (MOD-VEHICLE)**: أسطول المركبات والصيانة

## 🛠️ التقنيات المستخدمة

### Backend
- **Laravel 12** - PHP Framework
- **MySQL 8.0** - Database
- **Redis** - Caching & Sessions
- **Laravel Sanctum** - API Authentication
- **Spatie Permissions** - Role-based Access Control

### Frontend
- **Vue.js 3** - Progressive Framework
- **Inertia.js** - Modern Monolith
- **Tailwind CSS** - Utility-first CSS
- **PrimeVue** - UI Component Library
- **Chart.js** - Data Visualization

### Mobile
- **React Native** - Cross-platform Mobile Apps
- **Redux Toolkit** - State Management

## 📱 التطبيقات المحمولة

### تطبيق الحراس
- ✅ تسجيل الحضور والانصراف
- 📝 تقارير الحوادث السريعة
- 📞 التواصل مع الإدارة
- 📍 تحديد الموقع الحالي
- 🚨 زر الطوارئ

### تطبيق الإدارة
- 📊 لوحة معلومات فورية
- 📋 متابعة الحوادث
- 👥 إدارة الفرق
- 📈 تقارير سريعة
- 🔔 إشعارات مهمة

## 🔒 الأمان والحماية

- 🔐 **تشفير متقدم**: AES-256 للبيانات الحساسة
- 🔑 **مصادقة ثنائية**: 2FA لجميع المستخدمين
- 🛡️ **جدار حماية**: WAF للحماية من الهجمات
- 📝 **سجل العمليات**: تتبع جميع الأنشطة
- 🔄 **نسخ احتياطية**: تلقائية ومشفرة

## 📊 لوحات المعلومات

### لوحة الإدارة العليا
- 📈 مؤشرات الأداء الرئيسية (KPIs)
- 💰 التقارير المالية الفورية
- 👥 إحصائيات الموظفين
- 📊 تحليلات الأداء

### لوحة مدير العمليات
- 🗺️ خريطة المواقع والحراس
- ⏰ حالة الورديات الحالية
- 🚨 تنبيهات الحوادث
- 📋 المهام اليومية

## 🎯 الفوائد المتوقعة

### للإدارة
- ⚡ **تحسين الكفاءة** بنسبة 40%
- 💰 **تقليل التكاليف** بنسبة 25%
- 📊 **دقة البيانات** 99.5%
- ⚡ **قرارات أسرع** بالتقارير الفورية

### للموظفين
- 📱 **سهولة الاستخدام** عبر التطبيقات
- ⏰ **مرونة في التسجيل** (QR, GPS, كاميرا)
- 💰 **شفافية في الرواتب**
- 📞 **تواصل أفضل** مع الإدارة

### للعملاء
- ⭐ **خدمة أفضل** ومتابعة دقيقة
- 📊 **تقارير شفافة** عن الخدمات
- 💰 **فوترة دقيقة** وواضحة
- 📱 **تطبيق خاص** للمتابعة

## 📋 متطلبات النظام

### الخادم
- **PHP 8.2+**
- **MySQL 8.0+** أو **PostgreSQL 15+**
- **Redis 7.0+**
- **Node.js 18+**
- **Composer 2.6+**

### العميل
- **متصفح حديث** (Chrome, Firefox, Safari, Edge)
- **اتصال إنترنت** مستقر
- **دقة شاشة** 1024x768 أو أعلى

### الموبايل
- **Android 8.0+** أو **iOS 12.0+**
- **ذاكرة RAM** 2GB أو أكثر
- **مساحة تخزين** 100MB

## 🚀 التثبيت والإعداد

### 1. استنساخ المشروع
```bash
git clone https://github.com/your-repo/security-erp.git
cd security-erp
```

### 2. تثبيت التبعيات
```bash
# Backend dependencies
composer install

# Frontend dependencies
npm install
```

### 3. إعداد البيئة
```bash
# نسخ ملف البيئة
cp .env.example .env

# توليد مفتاح التطبيق
php artisan key:generate

# إعداد قاعدة البيانات في .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=security_erp
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 4. إعداد قاعدة البيانات
```bash
# تشغيل الهجرات
php artisan migrate

# تشغيل البذور (البيانات الأولية)
php artisan db:seed
```

### 5. تشغيل النظام
```bash
# تشغيل الخادم
php artisan serve

# تشغيل Vite للتطوير
npm run dev
```

## 📚 الوثائق التفصيلية

- 📋 [متطلبات البيجاكاجات](docs/packages-requirements.md)
- 📊 [التحليل الاستراتيجي](docs/strategic-analysis.md)
- 🎨 [الرسوم التوضيحية](docs/system-diagrams.md)
- 📝 [التحليل التفصيلي والاقتراحات](docs/detailed-analysis-and-suggestions.md)
- 📋 [الخطة الشاملة](docs/complete-project-plan.md)

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

### خطوات المساهمة
1. Fork المشروع
2. إنشاء فرع للميزة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل والدعم

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 XX XXX XXXX
- **الموقع**: https://security-erp.com
- **الدعم الفني**: https://support.security-erp.com

## 🏆 الفريق

- **مدير المشروع**: [اسم المدير]
- **المطور الرئيسي**: [اسم المطور]
- **مصمم UI/UX**: [اسم المصمم]
- **مهندس DevOps**: [اسم المهندس]

## 📈 خارطة الطريق

### المرحلة الأولى (3 أشهر) ✅
- [x] الموديولات الأساسية الثمانية
- [x] نظام المصادقة والصلاحيات
- [x] التطبيق الأساسي

### المرحلة الثانية (2 أشهر) 🔄
- [ ] الموديولات المتقدمة
- [ ] التطبيقات المحمولة
- [ ] تحسينات الأداء

### المرحلة الثالثة (2 أشهر) 📅
- [ ] الذكاء الاصطناعي
- [ ] التكامل مع الأنظمة الحكومية
- [ ] تقنيات متقدمة

## 🎉 شكر خاص

شكر خاص لجميع المساهمين والداعمين لهذا المشروع.

---

**© 2024 نظام ERP لشركات الأمن والحراسة. جميع الحقوق محفوظة.** Sponsors

We would like to extend our thanks to the following sponsors for funding Laravel development. If you are interested in becoming a sponsor, please visit the [Laravel Partners program](https://partners.laravel.com).

### Premium Partners

- **[Vehikl](https://vehikl.com/)**
- **[Tighten Co.](https://tighten.co)**
- **[WebReinvent](https://webreinvent.com/)**
- **[Kirschbaum Development Group](https://kirschbaumdevelopment.com)**
- **[64 Robots](https://64robots.com)**
- **[Curotec](https://www.curotec.com/services/technologies/laravel/)**
- **[Cyber-Duck](https://cyber-duck.co.uk)**
- **[DevSquad](https://devsquad.com/hire-laravel-developers)**
- **[Jump24](https://jump24.co.uk)**
- **[Redberry](https://redberry.international/laravel/)**
- **[Active Logic](https://activelogic.com)**
- **[byte5](https://byte5.de)**
- **[OP.GG](https://op.gg)**

## Contributing

Thank you for considering contributing to the Laravel framework! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

If you discover a security vulnerability within Laravel, please send an e-mail to Taylor Otwell via [<EMAIL>](mailto:<EMAIL>). All security vulnerabilities will be promptly addressed.

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).

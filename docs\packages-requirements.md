# متطلبات البيجاكاجات لنظام ERP شركة الأمن والحراسة

## 🔧 Backend Dependencies (Laravel/PHP)

### Core Framework & Database
```json
{
  "require": {
    "php": "^8.2",
    "laravel/framework": "^12.0",
    "laravel/tinker": "^2.10.1",
    "laravel/sanctum": "^4.0",
    "laravel/jetstream": "^5.0",
    "spatie/laravel-permission": "^6.0",
    "spatie/laravel-activitylog": "^4.8",
    "doctrine/dbal": "^4.0"
  }
}
```

### Authentication & Security
- **laravel/sanctum**: API authentication
- **spatie/laravel-permission**: إدارة الأدوار والصلاحيات
- **laravel/fortify**: Authentication backend
- **spatie/laravel-activitylog**: تسجيل العمليات

### Database & ORM
- **doctrine/dbal**: Database abstraction layer
- **laravel/scout**: البحث المتقدم
- **barryvdh/laravel-dompdf**: تصدير PDF
- **maatwebsite/excel**: تصدير Excel

### File Management & Media
- **spatie/laravel-medialibrary**: إدارة الملفات والصور
- **intervention/image**: معالجة الصور
- **league/flysystem-aws-s3-v3**: تخزين سحابي (اختياري)

### API & Communication
- **guzzlehttp/guzzle**: HTTP client
- **pusher/pusher-php-server**: Real-time notifications
- **laravel/horizon**: Queue monitoring
- **predis/predis**: Redis client

### Reporting & Analytics
- **barryvdh/laravel-dompdf**: PDF generation
- **maatwebsite/excel**: Excel export/import
- **spatie/laravel-query-builder**: API query building
- **spatie/laravel-backup**: نسخ احتياطية

### Development Tools
```json
{
  "require-dev": {
    "fakerphp/faker": "^1.23",
    "laravel/pail": "^1.2.2",
    "laravel/pint": "^1.13",
    "laravel/sail": "^1.41",
    "mockery/mockery": "^1.6",
    "nunomaduro/collision": "^8.6",
    "phpunit/phpunit": "^11.5.3",
    "laravel/telescope": "^5.0",
    "barryvdh/laravel-debugbar": "^3.9"
  }
}
```

## 🎨 Frontend Dependencies (Node.js/NPM)

### Core Framework
```json
{
  "dependencies": {
    "vue": "^3.4.0",
    "@inertiajs/vue3": "^1.0.0",
    "@inertiajs/inertia": "^0.11.1",
    "axios": "^1.7.4",
    "ziggy-js": "^2.0.0"
  }
}
```

### UI Framework & Styling
```json
{
  "devDependencies": {
    "@tailwindcss/vite": "^4.0.0",
    "tailwindcss": "^4.0.0",
    "@headlessui/vue": "^1.7.16",
    "@heroicons/vue": "^2.0.18",
    "primevue": "^3.45.0",
    "primeicons": "^6.0.1"
  }
}
```

### Charts & Data Visualization
- **chart.js**: الرسوم البيانية
- **vue-chartjs**: Vue wrapper for Chart.js
- **apexcharts**: رسوم بيانية متقدمة
- **vue3-apexcharts**: Vue 3 wrapper

### Form Handling & Validation
- **@vuelidate/core**: Form validation
- **@vuelidate/validators**: Built-in validators
- **vue-multiselect**: Multi-select components

### Date & Time
- **moment**: معالجة التواريخ
- **vue-datepicker**: Date picker component
- **@vuepic/vue-datepicker**: Modern date picker

### File Upload & Media
- **vue-upload-component**: رفع الملفات
- **cropperjs**: قص الصور
- **vue-cropper**: Vue wrapper for Cropper.js

### Development Tools
```json
{
  "devDependencies": {
    "vite": "^6.0.11",
    "laravel-vite-plugin": "^1.2.0",
    "concurrently": "^9.0.1",
    "@vitejs/plugin-vue": "^5.0.0",
    "eslint": "^8.57.0",
    "prettier": "^3.1.0"
  }
}
```

## 📱 Mobile App Dependencies (React Native - اختياري)

### Core Framework
- **react-native**: "^0.73.0"
- **@react-navigation/native**: Navigation
- **@react-navigation/stack**: Stack navigation
- **react-native-screens**: Native screens

### State Management
- **@reduxjs/toolkit**: State management
- **react-redux**: Redux bindings
- **redux-persist**: Persist state

### API & Authentication
- **axios**: HTTP client
- **@react-native-async-storage/async-storage**: Local storage
- **react-native-keychain**: Secure storage

### UI Components
- **react-native-elements**: UI toolkit
- **react-native-vector-icons**: Icons
- **react-native-paper**: Material Design

## 🗄️ Database & Infrastructure

### Database
- **MySQL 8.0+** أو **PostgreSQL 15+**
- **Redis 7.0+**: للـ cache والـ sessions
- **Elasticsearch 8.0+**: للبحث المتقدم (اختياري)

### Server Requirements
- **PHP 8.2+**
- **Node.js 18+**
- **Composer 2.6+**
- **NPM 9+ أو Yarn 1.22+**

### Cloud Services (اختياري)
- **AWS S3**: تخزين الملفات
- **Pusher**: Real-time notifications
- **Mailgun/SendGrid**: إرسال الإيميلات
- **Cloudflare**: CDN وحماية

## 🔒 Security Packages

### Laravel Security
- **spatie/laravel-csp**: Content Security Policy
- **spatie/laravel-honeypot**: Honeypot protection
- **pragmarx/google2fa-laravel**: Two-factor authentication
- **spatie/laravel-rate-limiting**: Rate limiting

### Frontend Security
- **dompurify**: XSS protection
- **helmet**: Security headers (if using Express.js)

## 📊 Analytics & Monitoring

### Application Monitoring
- **laravel/telescope**: Development debugging
- **spatie/laravel-ray**: Debug tool
- **bugsnag/bugsnag-laravel**: Error tracking
- **sentry/sentry-laravel**: Error monitoring

### Performance Monitoring
- **laravel/horizon**: Queue monitoring
- **spatie/laravel-query-builder**: Query optimization
- **barryvdh/laravel-debugbar**: Debug toolbar

## 🧪 Testing Packages

### Backend Testing
- **phpunit/phpunit**: Unit testing
- **laravel/dusk**: Browser testing
- **mockery/mockery**: Mocking
- **fakerphp/faker**: Test data generation

### Frontend Testing
- **@vue/test-utils**: Vue testing utilities
- **vitest**: Testing framework
- **jsdom**: DOM testing environment
- **cypress**: E2E testing

## 📦 Build & Deployment

### Build Tools
- **vite**: Frontend build tool
- **laravel-vite-plugin**: Laravel integration
- **concurrently**: Run multiple commands

### Deployment
- **deployer/deployer**: PHP deployment tool
- **laravel/envoy**: Task runner
- **docker**: Containerization (اختياري)

---

## 📋 Installation Commands

### Backend Setup
```bash
composer install
composer require spatie/laravel-permission
composer require spatie/laravel-activitylog
composer require barryvdh/laravel-dompdf
composer require maatwebsite/excel
composer require spatie/laravel-medialibrary
composer require intervention/image
```

### Frontend Setup
```bash
npm install
npm install vue @inertiajs/vue3
npm install primevue primeicons
npm install chart.js vue-chartjs
npm install @vuelidate/core @vuelidate/validators
```

### Development Tools
```bash
composer require --dev laravel/telescope
composer require --dev barryvdh/laravel-debugbar
npm install --save-dev eslint prettier
```

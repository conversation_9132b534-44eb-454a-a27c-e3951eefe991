# متطلبات البيجاكاجات لنظام ERP شركة الأمن والحراسة

## 🔧 Backend Dependencies (Laravel/PHP)

### Core Framework & Database
```json
{
  "require": {
    "php": "^8.2",
    "laravel/framework": "^12.0",
    "laravel/tinker": "^2.10.1",
    "laravel/sanctum": "^4.0",
    "laravel/jetstream": "^5.0",
    "spatie/laravel-permission": "^6.0",
    "spatie/laravel-activitylog": "^4.8",
    "doctrine/dbal": "^4.0"
  }
}
```

### Authentication & Security
- **laravel/sanctum**: API authentication
- **laravel/fortify**: Authentication backend مع 2FA
- **laravel/jetstream**: Complete authentication scaffolding
- **spatie/laravel-permission**: إدارة الأدوار والصلاحيات
- **spatie/laravel-activitylog**: تسجيل العمليات
- **pragmarx/google2fa-laravel**: Two-factor authentication
- **laravel/socialite**: Social login (Google, Facebook)
- **tymon/jwt-auth**: JWT authentication للـ APIs

### Database & ORM
- **doctrine/dbal**: Database abstraction layer
- **laravel/scout**: البحث المتقدم
- **barryvdh/laravel-dompdf**: تصدير PDF
- **maatwebsite/excel**: تصدير Excel

### File Management & Media
- **spatie/laravel-medialibrary**: إدارة الملفات والصور
- **intervention/image**: معالجة الصور
- **league/flysystem-aws-s3-v3**: تخزين سحابي (اختياري)

### API & Communication
- **guzzlehttp/guzzle**: HTTP client
- **pusher/pusher-php-server**: Real-time notifications
- **laravel/horizon**: Queue monitoring
- **predis/predis**: Redis client
- **laravel/websockets**: WebSocket server للإشعارات الفورية
- **spatie/laravel-flash**: Flash messages
- **filament/notifications**: Beautiful notification system

### Reporting & Analytics
- **barryvdh/laravel-dompdf**: PDF generation
- **maatwebsite/excel**: Excel export/import
- **spatie/laravel-query-builder**: API query building
- **spatie/laravel-backup**: نسخ احتياطية

### Development Tools
```json
{
  "require-dev": {
    "fakerphp/faker": "^1.23",
    "laravel/pail": "^1.2.2",
    "laravel/pint": "^1.13",
    "laravel/sail": "^1.41",
    "mockery/mockery": "^1.6",
    "nunomaduro/collision": "^8.6",
    "phpunit/phpunit": "^11.5.3",
    "laravel/telescope": "^5.0",
    "barryvdh/laravel-debugbar": "^3.9"
  }
}
```

## 🎨 Frontend Dependencies (Node.js/NPM)

### Core Framework
```json
{
  "dependencies": {
    "vue": "^3.4.0",
    "@inertiajs/vue3": "^1.0.0",
    "@inertiajs/inertia": "^0.11.1",
    "axios": "^1.7.4",
    "ziggy-js": "^2.0.0"
  }
}
```

### UI Framework & Styling
```json
{
  "devDependencies": {
    "@tailwindcss/vite": "^4.0.0",
    "tailwindcss": "^4.0.0",
    "@headlessui/vue": "^1.7.16",
    "@heroicons/vue": "^2.0.18",
    "primevue": "^3.45.0",
    "primeicons": "^6.0.1"
  }
}
```

### Authentication & Login System
```json
{
  "dependencies": {
    "@inertiajs/vue3": "^1.0.0",
    "vue-router": "^4.2.5",
    "@vueuse/core": "^10.5.0",
    "pinia": "^2.1.7",
    "pinia-plugin-persistedstate": "^3.2.0"
  }
}
```

### Notifications & Toast System
```json
{
  "dependencies": {
    "vue-toastification": "^2.0.0-rc.5",
    "vue-sonner": "^1.1.2",
    "@kyvg/vue3-notification": "^3.0.2",
    "vue-toast-notification": "^3.1.2"
  }
}
```

### Animations & Transitions
```json
{
  "dependencies": {
    "@vueuse/motion": "^2.0.0",
    "framer-motion": "^10.16.4",
    "animate.css": "^4.1.1",
    "aos": "^2.3.4",
    "lottie-web": "^5.12.2",
    "vue-lottie": "^0.2.1"
  }
}
```

### Charts & Data Visualization
- **chart.js**: الرسوم البيانية
- **vue-chartjs**: Vue wrapper for Chart.js
- **apexcharts**: رسوم بيانية متقدمة
- **vue3-apexcharts**: Vue 3 wrapper

### Form Handling & Validation
- **@vuelidate/core**: Form validation
- **@vuelidate/validators**: Built-in validators
- **vue-multiselect**: Multi-select components

### Date & Time
- **moment**: معالجة التواريخ
- **vue-datepicker**: Date picker component
- **@vuepic/vue-datepicker**: Modern date picker

### File Upload & Media
- **vue-upload-component**: رفع الملفات
- **cropperjs**: قص الصور
- **vue-cropper**: Vue wrapper for Cropper.js

### Development Tools
```json
{
  "devDependencies": {
    "vite": "^6.0.11",
    "laravel-vite-plugin": "^1.2.0",
    "concurrently": "^9.0.1",
    "@vitejs/plugin-vue": "^5.0.0",
    "eslint": "^8.57.0",
    "prettier": "^3.1.0"
  }
}
```

## 📱 Mobile App Dependencies (React Native - اختياري)

### Core Framework
- **react-native**: "^0.73.0"
- **@react-navigation/native**: Navigation
- **@react-navigation/stack**: Stack navigation
- **react-native-screens**: Native screens

### State Management
- **@reduxjs/toolkit**: State management
- **react-redux**: Redux bindings
- **redux-persist**: Persist state

### API & Authentication
- **axios**: HTTP client
- **@react-native-async-storage/async-storage**: Local storage
- **react-native-keychain**: Secure storage

### UI Components
- **react-native-elements**: UI toolkit
- **react-native-vector-icons**: Icons
- **react-native-paper**: Material Design

## 🗄️ Database & Infrastructure

### Database
- **MySQL 8.0+** أو **PostgreSQL 15+**
- **Redis 7.0+**: للـ cache والـ sessions
- **Elasticsearch 8.0+**: للبحث المتقدم (اختياري)

### Server Requirements
- **PHP 8.2+**
- **Node.js 18+**
- **Composer 2.6+**
- **NPM 9+ أو Yarn 1.22+**

### Cloud Services (اختياري)
- **AWS S3**: تخزين الملفات
- **Pusher**: Real-time notifications
- **Mailgun/SendGrid**: إرسال الإيميلات
- **Cloudflare**: CDN وحماية

## 🔒 Security Packages

### Laravel Security
- **spatie/laravel-csp**: Content Security Policy
- **spatie/laravel-honeypot**: Honeypot protection
- **pragmarx/google2fa-laravel**: Two-factor authentication
- **spatie/laravel-rate-limiting**: Rate limiting

### Frontend Security
- **dompurify**: XSS protection
- **helmet**: Security headers (if using Express.js)

## 📊 Analytics & Monitoring

### Application Monitoring
- **laravel/telescope**: Development debugging
- **spatie/laravel-ray**: Debug tool
- **bugsnag/bugsnag-laravel**: Error tracking
- **sentry/sentry-laravel**: Error monitoring

### Performance Monitoring
- **laravel/horizon**: Queue monitoring
- **spatie/laravel-query-builder**: Query optimization
- **barryvdh/laravel-debugbar**: Debug toolbar

## 🧪 Testing Packages

### Backend Testing
- **phpunit/phpunit**: Unit testing
- **laravel/dusk**: Browser testing
- **mockery/mockery**: Mocking
- **fakerphp/faker**: Test data generation

### Frontend Testing
- **@vue/test-utils**: Vue testing utilities
- **vitest**: Testing framework
- **jsdom**: DOM testing environment
- **cypress**: E2E testing

## 📦 Build & Deployment

### Build Tools
- **vite**: Frontend build tool
- **laravel-vite-plugin**: Laravel integration
- **concurrently**: Run multiple commands

### Deployment
- **deployer/deployer**: PHP deployment tool
- **laravel/envoy**: Task runner
- **docker**: Containerization (اختياري)

---

## 📋 Installation Commands

### Backend Setup
```bash
composer install
composer require spatie/laravel-permission
composer require spatie/laravel-activitylog
composer require barryvdh/laravel-dompdf
composer require maatwebsite/excel
composer require spatie/laravel-medialibrary
composer require intervention/image
```

### Frontend Setup
```bash
npm install
npm install vue @inertiajs/vue3
npm install primevue primeicons
npm install chart.js vue-chartjs
npm install @vuelidate/core @vuelidate/validators
```

### Development Tools
```bash
composer require --dev laravel/telescope
composer require --dev barryvdh/laravel-debugbar
npm install --save-dev eslint prettier
```

---

## 🔐 نظام التسجيل والدخول المتقدم

### Backend Authentication Packages
```bash
# نظام المصادقة الأساسي
composer require laravel/fortify
composer require laravel/jetstream

# المصادقة الثنائية
composer require pragmarx/google2fa-laravel
composer require bacon/bacon-qr-code

# تسجيل الدخول الاجتماعي
composer require laravel/socialite

# JWT للـ APIs
composer require tymon/jwt-auth

# تسجيل الأنشطة
composer require spatie/laravel-activitylog

# إدارة الجلسات المتقدمة
composer require pusher/pusher-php-server
```

### Frontend Authentication Components
```bash
# مكونات المصادقة
npm install @headlessui/vue
npm install @heroicons/vue

# إدارة الحالة
npm install pinia
npm install pinia-plugin-persistedstate

# التحقق من النماذج
npm install @vuelidate/core
npm install @vuelidate/validators

# QR Code للمصادقة الثنائية
npm install qrcode
npm install vue-qrcode-generator
```

---

## 🔔 نظام الإشعارات والتنبيهات المتقدم

### Backend Notifications
```bash
# إشعارات Laravel المتقدمة
composer require laravel/horizon
composer require pusher/pusher-php-server
composer require laravel/websockets

# إشعارات جميلة
composer require filament/notifications
composer require spatie/laravel-flash

# إرسال الإيميلات
composer require symfony/mailer
composer require mailgun/mailgun-php

# إرسال SMS
composer require twilio/sdk
composer require nexmo/laravel
```

### Frontend Toast & Notifications
```bash
# أنظمة Toast المختلفة (اختر واحد)
npm install vue-toastification        # الأكثر شعبية
npm install vue-sonner               # حديث وجميل
npm install @kyvg/vue3-notification  # مرن ومتقدم
npm install vue-toast-notification   # بسيط وسريع

# إشعارات متقدمة
npm install @vueuse/core
npm install @vueuse/integrations

# أصوات الإشعارات
npm install howler
```

### Animation & Effects Packages
```bash
# مكتبات الحركة والانيميشن
npm install @vueuse/motion           # Vue motion library
npm install framer-motion            # Advanced animations
npm install animate.css              # CSS animations
npm install aos                      # Animate on scroll
npm install lottie-web               # Lottie animations
npm install vue-lottie               # Vue Lottie wrapper

# تأثيرات بصرية
npm install particles.js
npm install tsparticles
npm install canvas-confetti
```

---

## 🎨 مكونات UI المتقدمة للنظام

### Advanced UI Components
```bash
# مكونات UI متقدمة
npm install primevue                 # Complete UI suite
npm install @primevue/themes         # PrimeVue themes
npm install primeicons              # PrimeVue icons

# مكونات إضافية
npm install vue-multiselect         # Multi-select dropdown
npm install @vuepic/vue-datepicker   # Advanced date picker
npm install vue-draggable-plus      # Drag and drop
npm install vue-virtual-scroller    # Virtual scrolling
npm install vue-loading-overlay     # Loading overlays
```

### Charts & Data Visualization
```bash
# مكتبات الرسوم البيانية
npm install chart.js
npm install vue-chartjs
npm install apexcharts
npm install vue3-apexcharts

# جداول متقدمة
npm install @tanstack/vue-table
npm install vue-good-table-next
```

---

## 📱 Real-time Features

### WebSocket & Real-time
```bash
# Backend real-time
composer require pusher/pusher-php-server
composer require laravel/websockets
composer require ratchet/pawl

# Frontend real-time
npm install pusher-js
npm install laravel-echo
npm install socket.io-client
```

### Progressive Web App (PWA)
```bash
# PWA support
npm install @vite-pwa/vite-plugin
npm install workbox-window
npm install register-service-worker
```

---

## 🔧 تكوين الإشعارات المقترح

### 1. Vue Toastification (الأكثر شعبية)
```javascript
// main.js
import Toast from "vue-toastification"
import "vue-toastification/dist/index.css"

app.use(Toast, {
  position: "top-right",
  timeout: 5000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: true,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: false,
  closeButton: "button",
  icon: true,
  rtl: true // للعربية
})
```

### 2. Laravel Flash Messages
```php
// Controller
return redirect()->back()->with('success', 'تم حفظ البيانات بنجاح');
return redirect()->back()->with('error', 'حدث خطأ في العملية');
return redirect()->back()->with('warning', 'تحذير: تحقق من البيانات');
return redirect()->back()->with('info', 'معلومة مهمة');
```

### 3. Real-time Notifications
```php
// Laravel Event
event(new \App\Events\NewNotification($user, $message));

// Vue component
Echo.private(`user.${userId}`)
    .listen('NewNotification', (e) => {
        this.$toast.success(e.message);
    });
```

---

## 🎯 أمثلة على الاستخدام

### Toast Notifications
```javascript
// نجاح العملية
this.$toast.success("تم حفظ البيانات بنجاح", {
  timeout: 3000,
  icon: "✅"
});

// خطأ في العملية
this.$toast.error("حدث خطأ في حفظ البيانات", {
  timeout: 5000,
  icon: "❌"
});

// تحذير
this.$toast.warning("تحقق من صحة البيانات المدخلة", {
  timeout: 4000,
  icon: "⚠️"
});

// معلومة
this.$toast.info("تم إرسال رسالة تأكيد إلى بريدك الإلكتروني", {
  timeout: 6000,
  icon: "ℹ️"
});
```

### Login System Features
```javascript
// تسجيل دخول عادي
await login(credentials);

// تسجيل دخول بالمصادقة الثنائية
await loginWith2FA(credentials, twoFactorCode);

// تسجيل دخول اجتماعي
await socialLogin('google');
await socialLogin('facebook');

// تذكر تسجيل الدخول
await login(credentials, { remember: true });

// تسجيل خروج من جميع الأجهزة
await logoutFromAllDevices();
```

# الرسوم التوضيحية والمخططات لنظام ERP

## 🏗️ مخطط البنية العامة للنظام

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Vue.js 3 + Inertia.js]
        B[Mobile App - React Native]
        C[Admin Dashboard]
    end
    
    subgraph "API Layer"
        D[Laravel 12 API]
        E[Authentication - Sanctum]
        F[Authorization - Spatie Permissions]
    end
    
    subgraph "Business Logic Layer"
        G[Employee Management]
        H[Attendance System]
        I[Payroll System]
        J[Accounting Module]
        K[Inventory Management]
        L[Client Management]
        M[Reporting Engine]
    end
    
    subgraph "Data Layer"
        N[MySQL Database]
        O[Redis Cache]
        P[File Storage]
        Q[Backup System]
    end
    
    subgraph "External Services"
        R[SMS Gateway]
        S[Email Service]
        T[Payment Gateway]
        U[Government APIs]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    D --> F
    D --> G
    D --> H
    D --> I
    D --> J
    D --> K
    D --> L
    D --> M
    G --> N
    H --> N
    I --> N
    J --> N
    K --> N
    L --> N
    M --> N
    D --> O
    D --> P
    N --> Q
    D --> R
    D --> S
    D --> T
    D --> U
```

## 📊 مخطط قاعدة البيانات (ERD)

```mermaid
erDiagram
    USERS {
        id bigint PK
        name varchar
        email varchar
        password varchar
        role_id bigint FK
        is_active boolean
        created_at timestamp
        updated_at timestamp
    }
    
    ROLES {
        id bigint PK
        name varchar
        guard_name varchar
        created_at timestamp
        updated_at timestamp
    }
    
    EMPLOYEES {
        id bigint PK
        employee_code varchar
        name varchar
        phone varchar
        email varchar
        national_id varchar
        position varchar
        department varchar
        hire_date date
        salary decimal
        status enum
        user_id bigint FK
        created_at timestamp
        updated_at timestamp
    }
    
    ATTENDANCES {
        id bigint PK
        employee_id bigint FK
        date date
        check_in time
        check_out time
        break_start time
        break_end time
        total_hours decimal
        overtime_hours decimal
        status enum
        notes text
        created_at timestamp
        updated_at timestamp
    }
    
    SALARIES {
        id bigint PK
        employee_id bigint FK
        month varchar
        year int
        basic_salary decimal
        allowances decimal
        deductions decimal
        overtime_amount decimal
        net_salary decimal
        status enum
        paid_at timestamp
        created_at timestamp
        updated_at timestamp
    }
    
    CLIENTS {
        id bigint PK
        client_code varchar
        name varchar
        contact_person varchar
        phone varchar
        email varchar
        address text
        contract_start date
        contract_end date
        monthly_amount decimal
        status enum
        created_at timestamp
        updated_at timestamp
    }
    
    SUPPLIERS {
        id bigint PK
        supplier_code varchar
        name varchar
        contact_person varchar
        phone varchar
        email varchar
        address text
        category varchar
        status enum
        created_at timestamp
        updated_at timestamp
    }
    
    PRODUCTS {
        id bigint PK
        product_code varchar
        name varchar
        description text
        category varchar
        unit varchar
        cost_price decimal
        selling_price decimal
        min_stock int
        current_stock int
        status enum
        created_at timestamp
        updated_at timestamp
    }
    
    PRODUCT_MOVEMENTS {
        id bigint PK
        product_id bigint FK
        type enum
        quantity int
        unit_price decimal
        total_amount decimal
        reference_type varchar
        reference_id bigint
        notes text
        created_at timestamp
        updated_at timestamp
    }
    
    EXPENSES {
        id bigint PK
        expense_code varchar
        category varchar
        description text
        amount decimal
        date date
        supplier_id bigint FK
        employee_id bigint FK
        status enum
        receipt_file varchar
        created_at timestamp
        updated_at timestamp
    }
    
    INCOMES {
        id bigint PK
        income_code varchar
        source varchar
        description text
        amount decimal
        date date
        client_id bigint FK
        status enum
        invoice_file varchar
        created_at timestamp
        updated_at timestamp
    }
    
    USERS ||--o{ EMPLOYEES : "has"
    USERS }o--|| ROLES : "belongs to"
    EMPLOYEES ||--o{ ATTENDANCES : "has"
    EMPLOYEES ||--o{ SALARIES : "receives"
    EMPLOYEES ||--o{ EXPENSES : "submits"
    CLIENTS ||--o{ INCOMES : "generates"
    SUPPLIERS ||--o{ EXPENSES : "receives"
    PRODUCTS ||--o{ PRODUCT_MOVEMENTS : "has"
```

## 🔄 مخطط تدفق العمليات الرئيسية

### 1. تدفق إدارة الموظفين

```mermaid
flowchart TD
    A[طلب توظيف جديد] --> B{موافقة المدير؟}
    B -->|نعم| C[إنشاء ملف الموظف]
    B -->|لا| D[رفض الطلب]
    C --> E[إنشاء حساب مستخدم]
    E --> F[تحديد الصلاحيات]
    F --> G[إرسال بيانات الدخول]
    G --> H[تفعيل الحساب]
    H --> I[بداية العمل]
    
    I --> J[تحديث البيانات]
    J --> K{طلب إنهاء خدمة؟}
    K -->|نعم| L[حساب المستحقات]
    K -->|لا| J
    L --> M[إنهاء الخدمة]
    M --> N[إلغاء تفعيل الحساب]
```

### 2. تدفق نظام الحضور والانصراف

```mermaid
flowchart TD
    A[وصول الموظف] --> B[تسجيل الحضور]
    B --> C{في الوقت المحدد؟}
    C -->|نعم| D[تسجيل حضور عادي]
    C -->|لا| E[تسجيل تأخير]
    
    D --> F[بداية العمل]
    E --> F
    F --> G[استراحة]
    G --> H[العودة من الاستراحة]
    H --> I[نهاية العمل]
    I --> J[تسجيل الانصراف]
    
    J --> K{ساعات إضافية؟}
    K -->|نعم| L[تسجيل الوقت الإضافي]
    K -->|لا| M[حساب ساعات العمل]
    L --> M
    M --> N[حفظ البيانات]
```

### 3. تدفق حساب المرتبات

```mermaid
flowchart TD
    A[بداية الشهر] --> B[جمع بيانات الحضور]
    B --> C[حساب ساعات العمل]
    C --> D[حساب الوقت الإضافي]
    D --> E[حساب الخصومات]
    E --> F[حساب البدلات]
    F --> G[حساب الراتب الصافي]
    
    G --> H{موافقة المدير؟}
    H -->|نعم| I[إنتاج كشف المرتبات]
    H -->|لا| J[مراجعة وتعديل]
    J --> G
    
    I --> K[إرسال للمحاسبة]
    K --> L[صرف المرتبات]
    L --> M[تحديث السجلات]
```

## 🎨 مخطط واجهة المستخدم (UI Flow)

```mermaid
flowchart TD
    A[صفحة تسجيل الدخول] --> B{بيانات صحيحة؟}
    B -->|نعم| C[لوحة التحكم الرئيسية]
    B -->|لا| A
    
    C --> D[إدارة الموظفين]
    C --> E[الحضور والانصراف]
    C --> F[المرتبات]
    C --> G[الحسابات]
    C --> H[المخازن]
    C --> I[العملاء]
    C --> J[التقارير]
    
    D --> D1[قائمة الموظفين]
    D --> D2[إضافة موظف]
    D --> D3[تعديل بيانات]
    
    E --> E1[تسجيل حضور]
    E --> E2[عرض الحضور]
    E --> E3[تقارير الحضور]
    
    F --> F1[حساب المرتبات]
    F --> F2[كشوف المرتبات]
    F --> F3[تقارير الرواتب]
    
    G --> G1[المصروفات]
    G --> G2[الإيرادات]
    G --> G3[التقارير المالية]
    
    H --> H1[المنتجات]
    H --> H2[حركة المخزن]
    H --> H3[تقارير المخزون]
    
    I --> I1[قائمة العملاء]
    I --> I2[العقود]
    I --> I3[الفواتير]
    
    J --> J1[تقارير الموظفين]
    J --> J2[التقارير المالية]
    J --> J3[تقارير المخزون]
```

## 🔐 مخطط الأمان والصلاحيات

```mermaid
flowchart TD
    A[مستخدم جديد] --> B[تسجيل الدخول]
    B --> C[التحقق من الهوية]
    C --> D{مصادقة ثنائية؟}
    D -->|نعم| E[إدخال رمز التحقق]
    D -->|لا| F[تحديد الدور]
    E --> F
    
    F --> G{نوع المستخدم}
    G -->|مدير عام| H[صلاحيات كاملة]
    G -->|مدير قسم| I[صلاحيات القسم]
    G -->|موظف إداري| J[صلاحيات محدودة]
    G -->|موظف أمن| K[صلاحيات أساسية]
    
    H --> L[الوصول للنظام]
    I --> L
    J --> L
    K --> L
    
    L --> M[تسجيل العمليات]
    M --> N[مراجعة الأنشطة]
```

## 📱 مخطط التطبيق المحمول

```mermaid
flowchart TD
    A[تطبيق الموبايل] --> B[تسجيل الدخول]
    B --> C[الشاشة الرئيسية]
    
    C --> D[تسجيل الحضور]
    C --> E[عرض الراتب]
    C --> F[طلب إجازة]
    C --> G[التقارير الشخصية]
    C --> H[الإشعارات]
    
    D --> D1[مسح QR Code]
    D --> D2[تحديد الموقع GPS]
    D --> D3[التقاط صورة]
    
    E --> E1[كشف الراتب]
    E --> E2[تاريخ المرتبات]
    
    F --> F1[طلب جديد]
    F --> F2[متابعة الطلبات]
    
    G --> G1[ساعات العمل]
    G --> G2[الحضور الشهري]
    
    H --> H1[إشعارات النظام]
    H --> H2[رسائل الإدارة]
```

## 🔄 مخطط التكامل مع الأنظمة الخارجية

```mermaid
flowchart LR
    A[نظام ERP] --> B[API Gateway]
    
    B --> C[نظام البنوك]
    B --> D[خدمة الرسائل SMS]
    B --> E[خدمة البريد الإلكتروني]
    B --> F[الأنظمة الحكومية]
    B --> G[أنظمة المحاسبة الخارجية]
    B --> H[خدمات التخزين السحابي]
    
    C --> C1[تحويل الرواتب]
    C --> C2[استعلام الأرصدة]
    
    D --> D1[إشعارات الحضور]
    D --> D2[تنبيهات الإدارة]
    
    E --> E1[كشوف المرتبات]
    E --> E2[التقارير الدورية]
    
    F --> F1[التأمينات الاجتماعية]
    F --> F2[الزكاة والضريبة]
    
    G --> G1[تصدير البيانات]
    G --> G2[استيراد البيانات]
    
    H --> H1[نسخ احتياطية]
    H --> H2[تخزين الملفات]
```

## 📊 مخطط الأداء والمراقبة

```mermaid
flowchart TD
    A[نظام المراقبة] --> B[مراقبة الخوادم]
    A --> C[مراقبة قاعدة البيانات]
    A --> D[مراقبة التطبيق]
    A --> E[مراقبة الشبكة]
    
    B --> B1[استخدام المعالج]
    B --> B2[استخدام الذاكرة]
    B --> B3[مساحة التخزين]
    
    C --> C1[أداء الاستعلامات]
    C --> C2[الاتصالات النشطة]
    C --> C3[حجم قاعدة البيانات]
    
    D --> D1[وقت الاستجابة]
    D --> D2[معدل الأخطاء]
    D --> D3[عدد المستخدمين]
    
    E --> E1[سرعة الاتصال]
    E --> E2[زمن الاستجابة]
    E --> E3[فقدان البيانات]
    
    B1 --> F[لوحة المراقبة]
    B2 --> F
    B3 --> F
    C1 --> F
    C2 --> F
    C3 --> F
    D1 --> F
    D2 --> F
    D3 --> F
    E1 --> F
    E2 --> F
    E3 --> F
    
    F --> G[تنبيهات تلقائية]
    F --> H[تقارير الأداء]
    F --> I[إجراءات تصحيحية]
```
